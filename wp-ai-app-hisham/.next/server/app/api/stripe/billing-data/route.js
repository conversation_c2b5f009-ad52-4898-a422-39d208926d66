/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/billing-data/route";
exports.ids = ["app/api/stripe/billing-data/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstripe%2Fbilling-data%2Froute&page=%2Fapi%2Fstripe%2Fbilling-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fbilling-data%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstripe%2Fbilling-data%2Froute&page=%2Fapi%2Fstripe%2Fbilling-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fbilling-data%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_WP_AI_wp_ai_app_hisham_src_app_api_stripe_billing_data_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/billing-data/route.ts */ \"(rsc)/./src/app/api/stripe/billing-data/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/billing-data/route\",\n        pathname: \"/api/stripe/billing-data\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/billing-data/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/WP-AI/wp-ai-app-hisham/src/app/api/stripe/billing-data/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_WP_AI_wp_ai_app_hisham_src_app_api_stripe_billing_data_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/stripe/billing-data/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstripe%2Fbilling-data%2Froute&page=%2Fapi%2Fstripe%2Fbilling-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fbilling-data%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/stripe/billing-data/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/stripe/billing-data/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _lib_stripe_customer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-customer */ \"(rsc)/./src/lib/stripe-customer.ts\");\n\n\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: \"2025-06-30.basil\",\n    typescript: true\n});\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVybWFheG5veWNrZXpianRlZ21xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMzI2NDgsImV4cCI6MjA2NjkwODY0OH0.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\");\n// Expects: Authorization: Bearer <supabase_jwt>\nasync function GET(req) {\n    try {\n        // 1. Get the Supabase JWT from the request\n        const authHeader = req.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n        // 2. Get the user from the JWT\n        const { data: { user }, error } = await supabase.auth.getUser(token);\n        if (error || !user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid user\"\n            }, {\n                status: 401\n            });\n        }\n        // 3. Get the Stripe customer ID from profiles table, create if doesn't exist\n        let stripeCustomerId = await (0,_lib_stripe_customer__WEBPACK_IMPORTED_MODULE_2__.getStripeCustomerIdFromProfile)(user.email);\n        if (!stripeCustomerId) {\n            // Create a new Stripe customer if one doesn't exist\n            try {\n                stripeCustomerId = await (0,_lib_stripe_customer__WEBPACK_IMPORTED_MODULE_2__.getOrCreateStripeCustomer)(user.id, user.email);\n            } catch (error) {\n                console.error(\"Error creating Stripe customer:\", error);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Failed to create Stripe customer\"\n                }, {\n                    status: 500\n                });\n            }\n        }\n        // 4. Fetch subscriptions, invoices, and payment methods from Stripe\n        const [subscriptions, invoices, paymentMethods] = await Promise.all([\n            stripe.subscriptions.list({\n                customer: stripeCustomerId,\n                limit: 1,\n                expand: [\n                    \"data.items.data.price.product\"\n                ]\n            }),\n            stripe.invoices.list({\n                customer: stripeCustomerId,\n                limit: 10\n            }),\n            stripe.paymentMethods.list({\n                customer: stripeCustomerId,\n                type: \"card\"\n            })\n        ]);\n        // Format the data to send to the frontend\n        const responseData = {\n            subscription: subscriptions.data[0] ? {\n                status: subscriptions.data[0].status,\n                plan: subscriptions.data[0].items.data[0]?.price.nickname || subscriptions.data[0].items.data[0]?.price.product?.name || \"N/A\",\n                amount: subscriptions.data[0].items.data[0]?.price.unit_amount / 100,\n                currency: subscriptions.data[0].items.data[0]?.price.currency,\n                nextBillingDate: subscriptions.data[0].items.data[0]?.current_period_end ? new Date(subscriptions.data[0].items.data[0].current_period_end * 1000).toISOString() : null\n            } : null,\n            invoices: invoices.data.map((invoice)=>({\n                    id: invoice.id,\n                    date: new Date(invoice.created * 1000).toISOString(),\n                    amount: invoice.amount_paid / 100,\n                    status: invoice.status,\n                    url: invoice.invoice_pdf\n                })),\n            paymentMethods: paymentMethods.data.map((pm)=>({\n                    id: pm.id,\n                    type: pm.type,\n                    last4: pm.card?.last4,\n                    brand: pm.card?.brand,\n                    isDefault: false\n                }))\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(responseData);\n    } catch (error) {\n        console.error(\"Stripe API Error:\", error.message);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal Server Error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/billing-data/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-customer.ts":
/*!************************************!*\
  !*** ./src/lib/stripe-customer.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getOrCreateStripeCustomer: () => (/* binding */ getOrCreateStripeCustomer),\n/* harmony export */   getStripeCustomerIdFromProfile: () => (/* binding */ getStripeCustomerIdFromProfile),\n/* harmony export */   updateStripeCustomerFromProfile: () => (/* binding */ updateStripeCustomerFromProfile)\n/* harmony export */ });\n/* harmony import */ var _stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stripe */ \"(rsc)/./src/lib/stripe.ts\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Create Supabase client with service role for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n/**\n * Get or create a Stripe customer for a Supabase user\n */ async function getOrCreateStripeCustomer(userId, userEmail) {\n    try {\n        // First, check if user already has a Stripe customer ID in profiles table\n        const { data: profile, error: profileError } = await supabaseAdmin.from(\"profiles\").select(\"stripe_customer_id, first_name, last_name, phone, address1, city, state_province, postal_code, country\").eq(\"email\", userEmail).single();\n        if (profileError && profileError.code !== \"PGRST116\") {\n            console.error(\"Error fetching profile:\", profileError);\n            throw new Error(\"Failed to fetch user profile\");\n        }\n        // If user already has a Stripe customer ID, verify it exists in Stripe\n        if (profile?.stripe_customer_id) {\n            try {\n                const customer = await _stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.customers.retrieve(profile.stripe_customer_id);\n                if (!customer.deleted) {\n                    return profile.stripe_customer_id;\n                }\n            } catch (error) {\n                console.warn(\"Stripe customer not found, creating new one:\", error);\n            }\n        }\n        // Create new Stripe customer\n        const customerData = {\n            email: userEmail\n        };\n        // Add name if available\n        if (profile?.first_name || profile?.last_name) {\n            customerData.name = `${profile.first_name || \"\"} ${profile.last_name || \"\"}`.trim();\n        }\n        // Add phone if available\n        if (profile?.phone) {\n            customerData.phone = profile.phone;\n        }\n        // Add address if available\n        if (profile?.address1 || profile?.city || profile?.state_province || profile?.postal_code || profile?.country) {\n            customerData.address = {\n                line1: profile.address1 || undefined,\n                city: profile.city || undefined,\n                state: profile.state_province || undefined,\n                postal_code: profile.postal_code || undefined,\n                country: profile.country || undefined\n            };\n        }\n        const customer = await _stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.customers.create(customerData);\n        // Update the profiles table with the new Stripe customer ID\n        const { error: updateError } = await supabaseAdmin.from(\"profiles\").update({\n            stripe_customer_id: customer.id\n        }).eq(\"email\", userEmail);\n        if (updateError) {\n            console.error(\"Error updating profile with Stripe customer ID:\", updateError);\n        // Don't throw here as the customer was created successfully\n        }\n        return customer.id;\n    } catch (error) {\n        console.error(\"Error in getOrCreateStripeCustomer:\", error);\n        throw new Error(\"Failed to get or create Stripe customer\");\n    }\n}\n/**\n * Get Stripe customer ID from user profile\n */ async function getStripeCustomerIdFromProfile(userEmail) {\n    try {\n        const { data: profile, error } = await supabaseAdmin.from(\"profiles\").select(\"stripe_customer_id\").eq(\"email\", userEmail).single();\n        if (error) {\n            console.error(\"Error fetching profile:\", error);\n            return null;\n        }\n        return profile?.stripe_customer_id || null;\n    } catch (error) {\n        console.error(\"Error in getStripeCustomerIdFromProfile:\", error);\n        return null;\n    }\n}\n/**\n * Update Stripe customer with latest profile information\n */ async function updateStripeCustomerFromProfile(stripeCustomerId, userEmail) {\n    try {\n        const { data: profile, error } = await supabaseAdmin.from(\"profiles\").select(\"first_name, last_name, phone, address1, city, state_province, postal_code, country\").eq(\"email\", userEmail).single();\n        if (error) {\n            console.error(\"Error fetching profile for update:\", error);\n            return;\n        }\n        const updateData = {};\n        // Update name if available\n        if (profile?.first_name || profile?.last_name) {\n            updateData.name = `${profile.first_name || \"\"} ${profile.last_name || \"\"}`.trim();\n        }\n        // Update phone if available\n        if (profile?.phone) {\n            updateData.phone = profile.phone;\n        }\n        // Update address if available\n        if (profile?.address1 || profile?.city || profile?.state_province || profile?.postal_code || profile?.country) {\n            updateData.address = {\n                line1: profile.address1 || undefined,\n                city: profile.city || undefined,\n                state: profile.state_province || undefined,\n                postal_code: profile.postal_code || undefined,\n                country: profile.country || undefined\n            };\n        }\n        if (Object.keys(updateData).length > 0) {\n            await _stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.customers.update(stripeCustomerId, updateData);\n        }\n    } catch (error) {\n        console.error(\"Error updating Stripe customer:\", error);\n    // Don't throw as this is not critical\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-customer.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe.ts":
/*!***************************!*\
  !*** ./src/lib/stripe.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stripe: () => (/* binding */ stripe)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n\nif (!process.env.STRIPE_SECRET_KEY) {\n    throw new Error(\"STRIPE_SECRET_KEY is not set\");\n}\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: \"2025-06-30.basil\",\n    typescript: true\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QjtBQUU1QixJQUFJLENBQUNDLFFBQVFDLEdBQUcsQ0FBQ0MsaUJBQWlCLEVBQUU7SUFDbEMsTUFBTSxJQUFJQyxNQUFNO0FBQ2xCO0FBRU8sTUFBTUMsU0FBUyxJQUFJTCw4Q0FBTUEsQ0FBQ0MsUUFBUUMsR0FBRyxDQUFDQyxpQkFBaUIsRUFBRTtJQUM5REcsWUFBWTtJQUNaQyxZQUFZO0FBQ2QsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvbGliL3N0cmlwZS50cz83OThhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTdHJpcGUgZnJvbSAnc3RyaXBlJztcblxuaWYgKCFwcm9jZXNzLmVudi5TVFJJUEVfU0VDUkVUX0tFWSkge1xuICB0aHJvdyBuZXcgRXJyb3IoJ1NUUklQRV9TRUNSRVRfS0VZIGlzIG5vdCBzZXQnKTtcbn1cblxuZXhwb3J0IGNvbnN0IHN0cmlwZSA9IG5ldyBTdHJpcGUocHJvY2Vzcy5lbnYuU1RSSVBFX1NFQ1JFVF9LRVksIHtcbiAgYXBpVmVyc2lvbjogJzIwMjUtMDYtMzAuYmFzaWwnLFxuICB0eXBlc2NyaXB0OiB0cnVlLFxufSk7XG4iXSwibmFtZXMiOlsiU3RyaXBlIiwicHJvY2VzcyIsImVudiIsIlNUUklQRV9TRUNSRVRfS0VZIiwiRXJyb3IiLCJzdHJpcGUiLCJhcGlWZXJzaW9uIiwidHlwZXNjcmlwdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstripe%2Fbilling-data%2Froute&page=%2Fapi%2Fstripe%2Fbilling-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fbilling-data%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();